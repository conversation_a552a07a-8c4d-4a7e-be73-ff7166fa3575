/* script.js - Implements hero dual-axis slider, logos pause, mobile schools slider, exhibition carousel */
/* Accessibility: keyboard controls, aria attributes, pause on hover/focus, respects prefers-reduced-motion */

document.addEventListener('DOMContentLoaded', function () {
  /* Utility: prefers reduced motion check */
  const reduceMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

  /* HERO slider */
  (function heroSlider() {
    const slidesEl = document.getElementById('heroSlides');
    const slides = Array.from(slidesEl.children);
    const viewport = document.getElementById('heroViewport');
    let index = 0;
    let axis = slidesEl.dataset.axis || 'horizontal';
    let playing = true;
    const playBtn = document.querySelector('.hero__btn--play');
    const prevBtn = document.querySelector('.hero__btn--prev');
    const nextBtn = document.querySelector('.hero__btn--next');
    const axisSelect = document.getElementById('axisSelect');

    function update() {
      slidesEl.dataset.axis = axis;
      if (axis === 'horizontal') {
        slidesEl.style.transform = `translateX(${-index * 100}%)`;
      } else {
        slidesEl.style.transform = `translateY(${-index * 100}%)`;
      }
      slides.forEach((s, i) => s.setAttribute('aria-hidden', i !== index));
    }

    function next() { index = (index + 1) % slides.length; update(); }
    function prev() { index = (index - 1 + slides.length) % slides.length; update(); }

    let timer = null;
    function startAuto() { if (!reduceMotion) timer = setInterval(next, 4500); playing = true; playBtn.setAttribute('aria-pressed','false'); playBtn.textContent = 'Pause'; }
    function stopAuto() { clearInterval(timer); timer = null; playing = false; playBtn.setAttribute('aria-pressed','true'); playBtn.textContent = 'Play'; }

    if (!reduceMotion) startAuto();

    playBtn.addEventListener('click', function () {
      if (playing) stopAuto(); else startAuto();
    });
    prevBtn.addEventListener('click', prev);
    nextBtn.addEventListener('click', next);
    axisSelect.addEventListener('change', function (e) { axis = e.target.value; update(); });

    viewport.addEventListener('mouseenter', () => { stopAuto(); });
    viewport.addEventListener('mouseleave', () => { if (!reduceMotion) startAuto(); });

    viewport.addEventListener('focusin', () => stopAuto());
    viewport.addEventListener('focusout', () => { if (!reduceMotion) startAuto(); });

    // keyboard controls
    viewport.addEventListener('keydown', (e) => {
      if (e.key === 'ArrowRight' || e.key === 'ArrowDown') next();
      if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') prev();
      if (e.key === ' ' || e.key === 'Spacebar') { e.preventDefault(); if (playing) stopAuto(); else startAuto(); }
    });

    // touch swipe
    let sx = 0, sy = 0;
    viewport.addEventListener('touchstart', (e) => { sx = e.changedTouches[0].clientX; sy = e.changedTouches[0].clientY; });
    viewport.addEventListener('touchend', (e) => {
      const dx = e.changedTouches[0].clientX - sx;
      const dy = e.changedTouches[0].clientY - sy;
      if (Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30) { if (dx < 0) next(); else prev(); }
      if (Math.abs(dy) > Math.abs(dx) && Math.abs(dy) > 30) { if (dy < 0) next(); else prev(); }
    });

    update();
  })();


  /* Logos: pause on hover/focus and keyboard focusable */
  (function logos() {
    const tracks = document.querySelectorAll('.logos__track--left, .logos__track--right');
    tracks.forEach(track => {
      track.setAttribute('tabindex', '0');
      track.addEventListener('focus', () => track.style.animationPlayState = 'paused');
      track.addEventListener('blur', () => track.style.animationPlayState = 'running');
      track.addEventListener('mouseenter', () => track.style.animationPlayState = 'paused');
      track.addEventListener('mouseleave', () => track.style.animationPlayState = 'running');
    });
  })();


  /* Schools mobile slider */
  (function mobileSchools() {
    const grid = document.getElementById('schoolsGrid');
    const mobileWrap = document.getElementById('schoolsMobile');
    const viewport = document.getElementById('schoolsViewport');
    const dotsWrap = document.getElementById('schoolsDots');
    const prevBtn = document.querySelector('.schools__nav--prev');
    const nextBtn = document.querySelector('.schools__nav--next');
    let initialized = false;
    let idx = 0;

    function init() {
      if (initialized) return;
      const cards = Array.from(grid.children);
      const track = document.createElement('div');
      track.className = 'slider__track';
      cards.forEach(c => {
        const item = document.createElement('div');
        item.className = 'slider__item';
        item.innerHTML = c.outerHTML;
        track.appendChild(item);
      });
      viewport.innerHTML = '';
      viewport.appendChild(track);

      // dots
      dotsWrap.innerHTML = '';
      cards.forEach((_, i) => {
        const d = document.createElement('button');
        d.className = 'dot';
        d.setAttribute('aria-label', 'Go to slide ' + (i + 1));
        d.addEventListener('click', () => { idx = i; move(); });
        dotsWrap.appendChild(d);
      });

      prevBtn.addEventListener('click', () => { idx = (idx - 1 + cards.length) % cards.length; move(); });
      nextBtn.addEventListener('click', () => { idx = (idx + 1) % cards.length; move(); });

      // swipe
      let sx = 0;
      viewport.addEventListener('touchstart', e => sx = e.changedTouches[0].clientX);
      viewport.addEventListener('touchend', e => {
        const dx = e.changedTouches[0].clientX - sx;
        if (dx < -30) idx = Math.min(cards.length - 1, idx + 1);
        if (dx > 30) idx = Math.max(0, idx - 1);
        move();
      });

      function move() {
        track.style.transform = `translateX(${-idx * 100}%)`;
        Array.from(dotsWrap.children).forEach((d, i) => d.classList.toggle('active', i === idx));
      }
      move();
      initialized = true;
    }

    function check() {
      if (window.innerWidth <= 640) {
        grid.style.display = 'none';
        mobileWrap.setAttribute('aria-hidden', 'false');
        init();
      } else {
        grid.style.display = '';
        mobileWrap.setAttribute('aria-hidden', 'true');
      }
    }
    window.addEventListener('resize', check);
    check();
  })();


  /* Exhibition slider */
  (function exhibition() {
    const track = document.getElementById('exhibitTrack');
    const prev = document.querySelector('.exhibition__nav--prev');
    const next = document.querySelector('.exhibition__nav--next');
    const viewport = document.getElementById('exhibitViewport');
    let idx = 0;

    function update() {
      const cards = Array.from(track.children);
      if (!cards.length) return;
      const cardWidth = cards[0].getBoundingClientRect().width + 16;
      track.style.transform = `translateX(${-idx * (cardWidth)}px)`;
      cards.forEach((c, i) => c.setAttribute('aria-hidden', i < idx || i > idx + 2));
    }

    prev.addEventListener('click', () => { idx = Math.max(0, idx - 1); update(); });
    next.addEventListener('click', () => { const max = Math.max(0, track.children.length - 3); idx = Math.min(max, idx + 1); update(); });

    window.addEventListener('resize', update);
    update();
  })();


  /* Simple form handling with accessible status updates */
  (function forms() {
    const enqForm = document.getElementById('enquiryForm');
    const enqStatus = document.getElementById('enquiryStatus');
    enqForm.addEventListener('submit', function (e) {
      e.preventDefault();
      enqStatus.textContent = 'Thank you — we will get back to you.';
      enqForm.reset();
    });

    const regForm = document.getElementById('registerForm');
    const regStatus = document.getElementById('registerStatus');
    regForm.addEventListener('submit', function (e) {
      e.preventDefault();
      regStatus.textContent = 'Registration received — check your email for confirmation.';
      regForm.reset();
    });
  })();

  /* Focus visible helper for keyboard outlines */
  (function focusVisible() {
    function handleFirstTab(e) {
      if (e.key === 'Tab') document.documentElement.classList.add('user-is-tabbing');
    }
    window.addEventListener('keydown', handleFirstTab);
  })();
});
