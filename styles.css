/* styles.css - BEM naming, semantic structure, responsive */

/* Reset & base */
* { box-sizing: border-box; }
html,body { height:100%; }
body { margin:0; font-family: Inter, system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>; color:#111; background:#fff; -webkit-font-smoothing:antialiased; -moz-osx-font-smoothing:grayscale; }
.container { max-width:1200px; margin:0 auto; padding:1rem; }

.visually-hidden { position:absolute !important; height:1px; width:1px; overflow:hidden; clip:rect(1px,1px,1px,1px); white-space:nowrap; border:0; padding:0; }

/* Skip link */
.skip-link { position: absolute; left:-999px; top:auto; width:1px; height:1px; overflow:hidden; z-index:1000; }
.skip-link:focus { left:1rem; top:1rem; width:auto; height:auto; background:#1f1f1f; color:#fff; padding:.5rem .75rem; border-radius:4px; text-decoration:none; }

/* Header */
.site-header { background:linear-gradient(90deg,#17103a,#5a1e7a); color:#fff; }
.site-header__inner { display:flex; align-items:center; gap:1rem; padding:1rem; }
.site-header__logo img { display:block; border-radius:6px; }
.site-nav__list { list-style:none; margin:0; padding:0; display:flex; gap:1rem; align-items:center; }
.site-nav__link { color:rgba(255,255,255,0.9); text-decoration:none; padding:.5rem; }
.btn { display:inline-block; border:0; padding:.7rem 1rem; border-radius:8px; cursor:pointer; text-decoration:none; font-weight:600; }
.btn--primary { background:#ffffff; color:#1a1a1a; padding:.6rem 1.1rem; border:2px solid rgba(255,255,255,0.06); }
.btn--secondary { background:#6a1b9a; color:#fff; }

/* Sections titles */
.section__title { font-size:1.5rem; text-align:center; margin:1.5rem 0; color:#2b1e79; }

/* HERO */
.hero { padding:2rem 0; background:linear-gradient(180deg,#23104a 0%, #5b1f82 60%); color:#fff; }
.hero__inner { display:grid; grid-template-columns: 1fr 540px; gap:2rem; align-items:start; align-items:stretch; position:relative; }
.hero__content { padding:2rem 1rem; max-width:520px; }
.hero__title { font-size:2.25rem; margin:0 0 .5rem 0; line-height:1.05; }
.hero__highlight { color:#f3c78a; }
.hero__subtitle { opacity:.9; margin-bottom:1rem; }
.hero__meta { display:flex; gap:1rem; flex-wrap:wrap; margin-top:1rem; }
.hero__meta-item { background:rgba(255,255,255,0.08); padding:.6rem .8rem; border-radius:48px; font-weight:600; }

/* carousel */
.hero__carousel { position:relative; width:100%; }
.hero__viewport { overflow:hidden; border-radius:18px; box-shadow:0 6px 30px rgba(0,0,0,0.25); }
.hero__slides { list-style:none; margin:0; padding:0; display:flex; transition:transform 700ms cubic-bezier(.2,.9,.2,1); width:300%; }
.hero__slide { min-width:100%; display:flex; align-items:center; justify-content:center; }
.hero__img { width:100%; height:360px; object-fit:cover; display:block; }

.hero__controls { display:flex; gap:.5rem; margin-top:.75rem; align-items:center; }
.hero__btn { background:rgba(255,255,255,0.12); padding:.6rem .8rem; border-radius:8px; color:#fff; border:0; cursor:pointer; }
.hero__axis { margin-top:.75rem; }
.hero__axis-select { padding:.4rem .5rem; border-radius:6px; border:0; }

/* enquiry form */
.hero__enquiry { position:absolute; right:0; top:1.5rem; width:340px; background:linear-gradient(180deg, rgba(255,255,255,0.06), rgba(255,255,255,0.02)); padding:1.25rem; border-radius:12px; box-shadow:0 10px 30px rgba(0,0,0,0.25); }
.hero__enquiry-title { margin:0 0 .75rem 0; font-size:1.25rem; }
.form__input { width:100%; padding:.65rem .75rem; border-radius:8px; border:1px solid rgba(255,255,255,0.08); background:rgba(255,255,255,0.04); color:#fff; margin-bottom:.6rem; }
.form__actions { display:flex; gap:.5rem; align-items:center; }

.form__status { margin-top:.5rem; font-size:.95rem; color:#e6f9e6; }

/* Logos scroller */
.logos { padding:2rem 0; background:#fff; }
.logos__scroller { overflow:hidden; position:relative; }
.logos__track { display:flex; gap:1.25rem; align-items:center; padding:1rem 0; }
.logos__item { min-width:140px; height:72px; display:flex; align-items:center; justify-content:center; background:#fff; border-radius:8px; box-shadow:0 4px 12px rgba(19,19,19,0.06); }
.logos__item img { max-width:120px; max-height:48px; object-fit:contain; }

/* continuous animation */
@keyframes scroll-left { from { transform: translateX(0);} to { transform: translateX(-50%);} }
@keyframes scroll-right { from { transform: translateX(0);} to { transform: translateX(50%);} }
.logos__track--left { animation: scroll-left 20s linear infinite; }
.logos__track--right { animation: scroll-right 22s linear infinite; }
.logos__track--left:focus, .logos__track--right:focus, .logos__track--left:hover, .logos__track--right:hover { animation-play-state:paused; outline:2px solid #6a1b9a; }

/* Schools grid */
.schools { padding:2rem 0; background:#fff; }
.schools__grid { display:grid; grid-template-columns:repeat(4,1fr); gap:1.5rem; }
.school-card { background:linear-gradient(180deg,#fff,#f8f8ff); border-radius:16px; overflow:hidden; box-shadow:0 10px 30px rgba(39,24,73,0.06); }
.school-card__img { width:100%; height:220px; object-fit:cover; display:block; }
.school-card__body { padding:1rem; }
.school-card__title { margin:0 0 .5rem 0; font-size:1.05rem; color:#2b1e79; }
.school-card__desc { margin:0; color:#444; }

/* mobile schools */
.schools__mobile { display:none; align-items:center; gap:.5rem; }
.schools__viewport { overflow:hidden; border-radius:12px; flex:1; }
.slider__track { display:flex; transition:transform 350ms ease; }
.slider__item { min-width:100%; padding:1rem; }

.schools__nav { background:#fff; border-radius:8px; padding:.4rem .6rem; border:1px solid rgba(0,0,0,0.08); cursor:pointer; }

.schools__dots { display:flex; gap:.4rem; justify-content:center; margin-top:.6rem; }
.schools__dots .dot { width:9px; height:9px; border-radius:50%; background:#ddd; border:none; }
.schools__dots .dot.active { background:#6a1b9a; }

/* Exhibition slider */
.exhibition { padding:2rem 0; background:#faf7ff; }
.exhibition__carousel { display:flex; gap:1rem; align-items:center; }
.exhibition__viewport { overflow:hidden; border-radius:12px; }
.exhibition__track { display:flex; gap:1rem; transition:transform 450ms ease; }
.exhibit-card { min-width:260px; background:#fff; border-radius:12px; padding:1rem; box-shadow:0 6px 20px rgba(18,12,35,0.06); }

.exhibition__nav { background:#fff; border:0; padding:.4rem .8rem; border-radius:8px; cursor:pointer; }

/* Footer */
.site-footer { padding:1rem 0; background:#1f1f1f; color:#fff; text-align:center; }

/* Responsive */
@media(max-width:1000px) {
  .hero__inner { grid-template-columns: 1fr; }
  .hero__enquiry { position:static; width:auto; margin-top:1rem; }
  .hero__img { height:260px; }
  .schools__grid { grid-template-columns:repeat(2,1fr); }
}
@media(max-width:640px) {
  .site-header__inner { padding:.6rem; }
  .section__title { font-size:1.25rem; }
  .schools__grid { grid-template-columns:1fr; }
  .hero__img { height:180px; }
  .logos__item { min-width:100px; height:56px; }
  .schools__mobile { display:flex; }
  .schools__grid { display:none; }
}

/* prefers-reduced-motion */
@media (prefers-reduced-motion: reduce) {
  .hero__slides, .logos__track--left, .logos__track--right, .slider__track, .exhibition__track { transition: none !important; animation: none !important; }
}
