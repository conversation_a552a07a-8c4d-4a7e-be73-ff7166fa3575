Premier Schools Exhibition - Accessible Landing (Starter)

Files included:
- index.html : main markup (semantic HTML5 + ARIA)
- styles.css : custom CSS (BEM-like classes), responsive, respects prefers-reduced-motion
- script.js  : slider logic for hero (dual-axis), logos scroller pause, mobile school slider, exhibition carousel
- assets/images : placeholder SVG images and logos you can replace

Accessibility notes:
- Skip-to-content link, keyboard navigation for sliders, aria-roledescription and aria-live used.
- Animations pause on hover/focus; respects prefers-reduced-motion media query.
- Validate with W3C HTML validator and axe for accessibility checks.

How to use:
1. Unzip the package and open index.html in a browser.
2. Replace images in assets/images with final assets from Figma.
3. Run axe or Lighthouse for accessibility scoring and iterate as needed.
